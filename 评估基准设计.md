参考：

ChemLLMBench

要素：

1. 评测能力：如分子性质预测、逆合成分析、反应产率预测、化学知识问答等
2. 任务定义：具体任务类型和格式
3. 数据集：数据集来源、规模、数据格式（如SMILES, SELFIES）及关键的预处理步骤
4. 评估方法：
   * 评估指标（例如：Accuracy, F1 Score, MAE, R², Molecule Similarity等
   * 评估脚本，并提供其来源或链接
5. 基线模型及SOTA：列出基线模型及其表现
6. 输入输出格式(prompt)
7. 排行榜：说明该基准是否有公开的排行榜

# 1 ChemLLMBench

题目：What can Large Language Models do in chemistry? A comprehensive benchmark on eight tasks

url：https://arxiv.org/html/2305.18365v3

## 1.1 关注领域

## 1.2 任务定义

| 能力      | 任务               | 任务类型       | 数据                                            | 基线模型    | 评估指标                                                     |
| --------- | ------------------ | -------------- | ----------------------------------------------- | ----------- | ------------------------------------------------------------ |
| 理解      | 名称预测           | Generation     | PubChem                                         |             | Accuracy                                                     |
|           | 性质预测           | Classification | BBBP, HIV,BACE,Tox21, ClinTox                   | MoIR (GNN)  | Accuracy, F1 score                                           |
| 推理/预测 | 产量预测           | Classification | Buchwald-Hartwig,Suzuki-Miyaura Suzuki-Miyaura, | UAGNN       | Accuracy                                                     |
|           | 反应预测           | Generation     | USPTO-Mixed                                     | Chemformer  | Accuracy，Validity                                           |
|           | 试剂选择           | Ranking        | Suzuki-Miyaura                                  |             | Accuracy                                                     |
|           | 逆合成             | Generation     | USPTO-50k                                       | Chemformer  | Accuracy，Validity                                           |
|           | 基于文本的分子设计 | Generation     | ChEBI-20                                        | MolT5-Large | **NLP指标**: BLEU, 精确匹配 (Exact Match), Levenshtein distance 。<br/>**化学相似度**: Fingerprint Tanimoto Similarity (FTS), Fréchet ChemNet Distance (FCD) 。<br/>**有效性**: Validity 。 |
| 解释      | 分子描述生成       | Generation     | ChEBI-20                                        | MolT5-Large | BLEU, Chemists, etc                                          |

## 1.3 基线模型

GPT-4, GPT-3.5-turbo, Davinci-003, LLama and Galactica

## 1.4 prompt

{通用模板}{特定任务模板}{ICL}{问题}

<img src="评估基准设计.assets/image-20250908104402012.png" alt="image-20250908104402012" style="zoom:50%;" />

## 1.5 评估脚本/数据集

https://github.com/ChemFoundationModels/ChemLLMBench

# 2.Mol-Instructioins

题目：Mol-Instructions: A Large-Scale Biomolecular Instruction Dataset for Large Language Models（ICLR 2024）

url：https://arxiv.org/abs/2306.08018

github：https://github.com/zjunlp/Mol-Instructions?tab=readme-ov-file

评估代码：https://github.com/zjunlp/Mol-Instructions/tree/main/evaluation

## 2.1 测评能力

| 能力                             | 任务                           | 任务类型                                          | 数据 | 评估指标                                                     |
| -------------------------------- | ------------------------------ | ------------------------------------------------- | ---- | ------------------------------------------------------------ |
| 分子生成任务                     | 基于描述的分子设计             | Generation                                        |      | Exact<br />BLEU<br />Levenshtein<br />RDK FTS<br />MACC FTS<br />Morgan FTS<br />Validity |
|                                  | 正向反应预测                   | Generation                                        |      | 同上                                                         |
|                                  | 逆合成                         | Generation                                        |      | 同上                                                         |
|                                  | 反应试剂预测（试剂溶剂催化剂） | Generation                                        |      | 同上                                                         |
| 分子性质预测任务                 | 分子性质预测                   | Regression                                        |      | MAE                                                          |
| 分子理解任务                     | 分子描述生成                   | Generation                                        |      | BLEU-2<br />BLEU-4<br />ROUGE-1<br />ROUGE-2<br />ROUGE-L<br />METEOR |
| 蛋白质理解                       | 蛋白质功能                     | Generation                                        |      | ROUGE-L                                                      |
|                                  | 功能描述                       | Generation                                        |      | ROUGE-L                                                      |
|                                  | 催化活性                       | Generation/Prediction                             |      | ROUGE-L                                                      |
|                                  | 结构域/基序                    | Prediction/Sequence                               |      | ROUGE-L                                                      |
| **生物学NLP任务**-问答和信息抽取 | 判断题                         | Classification                                    |      | Acc                                                          |
|                                  | 多选                           | Classification                                    |      | Acc                                                          |
|                                  | 化学实体识别                   | Sequence Tagging <br />/ Named Entity Recognition |      | F1                                                           |
|                                  | 化学-疾病相互作用抽取          | Relation Extraction                               |      | F1                                                           |
|                                  | 化学-蛋白质相互作用抽取        | Relation Extraction                               |      | F1                                                           |
| **生物学NLP任务**-开放问题       | 开放问题                       | Generation Task / Open-domain Question Answering  |      | BLEU<br />ROUGE-1<br />BertScore                             |

## 2.2 评估指标

分子任务:

1. 文本生成：

   * **BLUE**
   * **ROUGE**
   * **METEOR**

2. 分子生成

   * **Validity:** 生成分子的有效率（使用RDKit验证）
   * **Exact Match:** 与参考答案的精确匹配度

   分子相似性：

   * **RDK FTS**
   * **MACC FTS**
   * **Morgan FTS**
   * **Levenshtein**
   * **BLEU**

   性质预测：

   * **MAE**

3. 蛋白质任务

   * **ROUGE：**

4. 生物文本任务

   

## 2.3 基线模型

**基线模型：**

Alpaca、<br />Baize、<br />ChatGLM、<br />LLaMa、<br />Vicuna、<br />Galactica、<br />Text+Chem T5

## 2.4 输入输出

### 2.4.1 分子

分子描述生成：

```
Instruction: Please give me some details about this molecule.
Input: [C][C][C][C][C][C][C]... (分子的SELFIES字符串)
Output (ground truth): The molecule is a 3-sn-phosphatidyl-L-serine in which the phosphatidyl acyl groups at positions 1 and 2 are palmitoyl and stearoyl respectively. It has a role as a mouse metabolite... (分子的详细文字描述)
```

逆合成分析

```
Instruction: Please suggest potential reactants used in the synthesis of the provided product.
Input: [C][C][C][C][N][C]... (产物的SELFIES字符串)
Output (ground truth): [C][C][C][C][N][C]...[Branch1][C][C][C].[C][O]... (反应物的SELFIES字符串，不同反应物用'.'分隔)
```

正向反应预测

![image-20250908153457723](评估基准设计.assets/image-20250908153457723.png)

试剂预测

![image-20250908153603069](评估基准设计.assets/image-20250908153603069.png)

基于描述的分子设计

![image-20250908153647438](评估基准设计.assets/image-20250908153647438.png)

性质预测

![image-20250908153847549](评估基准设计.assets/image-20250908153847549.png)

### 2.4.2 蛋白质

蛋白质功能预测

![image-20250908154005656](评估基准设计.assets/image-20250908154005656.png)

蛋白质设计

![image-20250908154103441](评估基准设计.assets/image-20250908154103441.png)











# 7.Sci-LLMs

## 7.1 相关工作

ScienceQA：☑️

MMLU‑Pro：☑️

ResearchBench：覆盖12个学科的、大规模的benchmark，评估LLMs的假设生成能力

ScienceAgentBench：科学工作流程

MultiAgentBench、WorkflowBench：衡量协作、协调和工作流



**化学相关：**

ChemBench:涵盖就和核心化学任务的综合性基准

**模型：**

ChemLLM

InstructMol

ChemDFM

ChemMLLM☑️

Chem3DLLM



## 7.1 ChemMLLM

https://github.com/bbsbz/ChemMLLM

| 任务                             | 任务描述                                                     | 任务类型 | 基线模型                                                     | 评估指标                                                     |
| -------------------------------- | ------------------------------------------------------------ | -------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 分子图像描述（img2caption）      | 分子图像生成 一个描述其来源、功能、结构特征和用途的说明      | 文本生成 |                                                              | **BLEU-2** (↑)<br />**BLEU-4** (↑)<br />**ROUGE-1** (↑)<br />**ROUGE-2** (↑)<br />**ROUGE-L** (↑)<br />**METEOR** (↑) |
| 分子图像属性预测（img2property） | 为图像生成：分子量（MW）、辛醇-水分配系数（P，指溶质在辛醇和水之间的分配系数，LogP）、拓扑极表面积（TPSA）、氢键供体（Hbd）、氢键受体（Hba）、可旋转键（Rb）和药物相似性定量估计（QED） | 回归预测 | Chemception (CNN)<br />Chemprop (GNN)<br />LLM：Qwen-VL-Chat (7B)<br />InternVL-Chat-v1.5 (20B) | MW、LogP、TPSA、Hbd、Hba、Rb算****、**MSE**、**MAE**<br />QED算Pearson、MSE<br />最后算valid<br /> |
| 图像到 SMILES 转换（img2smiles） | 精确识别原子、键、环和立体化学                               |          | MolScribe、Decimer                                           | **Avg Sim**（Tanimoto 相似度）<br />**ACC**<br />**valid**   |
| 分子性质到图像（property2img）   | 在目标性质条件下生成分子图像，需要同时优化多个性质约束，抱持化学有效性<br />对于无法生成图像的MLLM是img2smiles<br />无法直接从图像优化的，是property2smiles |          |                                                              | 生成的图像用MolScribe转为smiles，计算属性，使用MSE、MAE 、Pearson |
| 分子图像优化（img2img）          | 将具有较差分子性质（例如，LogP）的分子结构作为输入，并生成具有更优性质的相似分子结构，同时保留期望的化学性质<br />对于无法生成图像的MLLM是img2smiles<br />无法直接从图像优化的，是smiles2smiles |          | ![image-20250910192749152](评估基准设计.assets/image-20250910192749152.png) | **Increased LogP** (↑)<br />**Diversity** (↑)<br />**Novelty** (↑)<br />**valid%**(↑) |

指标的解释如下：

**img2caption：**

* **BLEU (Bilingual Evaluation Understudy, ↑)**: 最初用于机器翻译的指标，衡量生成文本中n-gram（连续n个词的序列）的精确率。`BLEU-2` 和 `BLEU-4` 分别使用2-gram和4-gram进行评估，越高说明生成的短语与参考答案匹配得越好。

![image-20250910191848918](评估基准设计.assets/image-20250910191848918.png)

* **ROUGE (Recall-Oriented Understudy for Gisting Evaluation, ↑)**: 主要用于文本摘要任务，衡量生成文本的召回率。
  - `ROUGE-1` 和 `ROUGE-2` 分别衡量单个词（unigram）和词对（bigram）的重叠情况。
  - `ROUGE-L` 则衡量生成文本与参考答案之间的最长公共子序列（Longest Common Subsequence），更关注句子层面的流畅度和结构。

![image-20250910191903855](评估基准设计.assets/image-20250910191903855.png)

![image-20250910191916358](评估基准设计.assets/image-20250910191916358.png)

* **METEOR (Metric for Evaluation of Translation with Explicit ORdering, ↑)**: 一个比BLEU和ROUGE更复杂的指标。它不仅考虑精确匹配，还考虑了同义词、词干等，并对词序进行了惩罚，通常被认为与人类的判断有更好的一致性。

![image-20250910191927354](评估基准设计.assets/image-20250910191927354.png)

****

**img2property**

* **Pearson (Pearson Correlation Coefficient, ↑)**: PC，皮尔逊相关系数。衡量模型预测值与真实值之间的线性相关程度。数值范围在-1到1之间，越接近1表示正相关性越强，性能越好。箭头 `↑` 表示该指标越高越好。

![image-20250910192025631](评估基准设计.assets/image-20250910192025631.png)

* **MSE (Mean Squared Error, ↓)**: 均方误差。计算预测值与真实值之差的平方的平均值。它对异常值（即误差很大的预测）非常敏感。箭头 `↓` 表示该指标越低越好。

![image-20250910191955053](评估基准设计.assets/image-20250910191955053.png)

* **MAE (Mean Absolute Error, ↓)**: 平均绝对误差。计算预测值与真实值之差的绝对值的平均值。相比MSE，它对异常值的鲁棒性更好。箭头 `↓` 表示该指标越低越好。

![image-20250910192003069](评估基准设计.assets/image-20250910192003069.png)

* **valid% (↑)**: 有效性百分比。这个指标很可能衡量模型能够成功解析输入图像并给出有效预测（例如，一个非空的、格式正确的数值）的比例。对于某些无法理解分子图像的模型，这个比例可能会很低。箭头 `↑` 表示该指标越高越好。

****

**img2smiles**

* **Tanimoto 相似性：**用于测量两个分子之间的相似度。Tanimoto 相似性也称为 Jaccard 系数，即两个化学指纹向量交集与并集的比值

![image-20250910192214453](评估基准设计.assets/image-20250910192214453.png)



# 4. ChemVLM

## 4.1评估任务

做了三个bench：

1. OCSR：ChemOCR
2. MMCR：MMCR-Bench+CMMU+ScienceQA  
3. 多模态分子理解：MMChemBench（ChemBench 的扩展）

| 能力                     | 任务         | 数据集                    | 评估指标                                               |
| ------------------------ | ------------ | ------------------------- | ------------------------------------------------------ |
| 光学化学结构识别（OCSR） |              |                           | RDKIT计算average Tanimoto similarity、Tanimoto hit 1.0 |
| 多模态化学推理（MMCR）   |              | MMCR-Bench+CMMU+ScienceQA | 零样本做题、计算ACC                                    |
| 多模态分子理解           | 分子描述     | MMChemBench               |                                                        |
|                          | 分子性质预测 | MMChemBench               |                                                        |
| 泛化能力                 |              | CMMU的其他学科            |                                                        |
| 更复杂任务               |              | Scibench                  |                                                        |

## 4.2 未来工作

添加新的模态：分子图和时间序列

开发不同参数规模的模型：

# 5 ChemCrow

是agent，待看

题目：Augmenting large language models with chemistry tools

| 能力         | 任务 | 数据集 | 评估指标 |
| ------------ | ---- | ------ | -------- |
| 有机合成任务 | 路线 |        |          |

# 6 其他参考

| 能力               | 任务               | benchmark                                                    |
| ------------------ | ------------------ | ------------------------------------------------------------ |
| 化学推理与知识整合 | 科学问答           | ChemBench，<br />Mol-Instructions☑️                           |
|                    | 机理阐释与多步推理 | REVEALING CHEMICAL REASONING IN LLMS THROUGHSEARCHONCOMPLEXPLANNINGTASKS<br />https://openreview.net/pdf?id=l5V9JB3bQc☑️ |
|                    | 多模态理解         | https://arxiv.org/pdf/2409.13194<br />https://arxiv.org/html/2508.01670v1<br />https://chemrxiv.org/engage/chemrxiv/article-details/689bd3f6728bf9025e3524fb |
|                    | 反应分类           |                                                              |
|                    |                    |                                                              |



各种bench对比：

MC代表多选，FR代表开放式回答

T代表基于文本的问题，I代表给予图像的问题

第四列表示是否有超过90%的问题带有解释

![image-20250910001708525](./评估基准设计.assets/image-20250910001708525.png)





![image-20250908182738210](评估基准设计.assets/image-20250908182738210.png)

任务描述：

1、对现有的化学及通用领域Benchmark（含多模态Benchmark）进行调研

交付内容：

调研报告，包括备选bench，测评目标，使用数据集，优缺点