#!/usr/bin/env python3
"""
快速查看 MMLU-Pro Parquet 文件的简化脚本
"""

import pandas as pd
import os

def main():
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parquet_file = os.path.join(script_dir, "test-00000-of-00001.parquet")
    
    try:
        print("正在读取 MMLU-Pro 数据...")
        df = pd.read_parquet(parquet_file)
        
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print("\n前3行数据:")
        print(df.head(3))
        
        # 如果有特定的列，显示一些统计信息
        if 'question' in df.columns:
            print(f"\n问题数量: {len(df)}")
        
        if 'category' in df.columns or 'subject' in df.columns:
            category_col = 'category' if 'category' in df.columns else 'subject'
            print(f"\n{category_col} 分布:")
            print(df[category_col].value_counts().head(10))
            
    except Exception as e:
        print(f"错误: {e}")
        print("请确保已安装 pandas 和 pyarrow: pip install pandas pyarrow")

if __name__ == "__main__":
    main()
