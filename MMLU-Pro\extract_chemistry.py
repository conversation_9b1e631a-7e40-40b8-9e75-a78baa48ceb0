#!/usr/bin/env python3
"""
提取MMLU-Pro数据集中category为chemistry的数据并保存为JSON格式
"""

import pandas as pd
import json
import os
import numpy as np

def extract_chemistry_data():
    """提取chemistry类别的数据"""
    
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parquet_file = os.path.join(script_dir, "test-00000-of-00001.parquet")
    
    try:
        print("正在读取MMLU-Pro数据...")
        df = pd.read_parquet(parquet_file)
        
        print(f"总数据量: {len(df)} 条")
        print(f"所有类别: {sorted(df['category'].unique())}")
        
        # 筛选chemistry类别的数据
        chemistry_df = df[df['category'] == 'chemistry'].copy()
        
        print(f"\nchemistry类别数据量: {len(chemistry_df)} 条")
        
        if len(chemistry_df) == 0:
            print("没有找到chemistry类别的数据")
            return
        
        # 显示chemistry数据的基本信息
        print(f"chemistry数据来源: {chemistry_df['src'].unique()}")
        print(f"chemistry数据答案分布: {chemistry_df['answer'].value_counts().to_dict()}")
        
        # 转换为JSON格式的数据结构
        chemistry_data = []
        
        for idx, row in chemistry_df.iterrows():
            # 处理options列（可能是字符串形式的列表或numpy数组）
            options = row['options']
            if hasattr(options, 'tolist'):
                # 如果是numpy数组，转换为列表
                options = options.tolist()
            elif isinstance(options, str):
                try:
                    # 尝试解析字符串形式的列表
                    import ast
                    options = ast.literal_eval(options)
                except:
                    # 如果解析失败，保持原样
                    pass

            # 确保所有值都是JSON可序列化的
            data_item = {
                "question_id": int(row['question_id']),
                "question": str(row['question']),
                "options": options,
                "answer": str(row['answer']),
                "answer_index": int(row['answer_index']),
                "cot_content": str(row['cot_content']) if pd.notna(row['cot_content']) else None,
                "category": str(row['category']),
                "src": str(row['src'])
            }
            chemistry_data.append(data_item)
        
        # 保存为JSON文件
        output_file = os.path.join(script_dir, "chemistry_data.json")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(chemistry_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 已成功提取 {len(chemistry_data)} 条chemistry数据")
        print(f"📁 保存位置: {output_file}")
        
        # 显示前几条数据的预览
        print(f"\n📋 前3条数据预览:")
        for i, item in enumerate(chemistry_data[:3]):
            print(f"\n--- 第 {i+1} 条 ---")
            print(f"ID: {item['question_id']}")
            print(f"问题: {item['question'][:100]}...")
            print(f"选项数量: {len(item['options']) if isinstance(item['options'], list) else 'N/A'}")
            print(f"答案: {item['answer']}")
            print(f"来源: {item['src']}")
        
        # 创建简化版本（只包含核心字段）
        simplified_data = []
        for item in chemistry_data:
            simplified_item = {
                "id": item['question_id'],
                "question": item['question'],
                "options": item['options'],
                "answer": item['answer'],
                "answer_index": item['answer_index']
            }
            simplified_data.append(simplified_item)
        
        # 保存简化版本
        simplified_file = os.path.join(script_dir, "chemistry_data_simplified.json")
        with open(simplified_file, 'w', encoding='utf-8') as f:
            json.dump(simplified_data, f, ensure_ascii=False, indent=2)
        
        print(f"📁 简化版本保存位置: {simplified_file}")
        
        return chemistry_data
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("MMLU-Pro Chemistry数据提取器")
    print("=" * 50)
    
    chemistry_data = extract_chemistry_data()
    
    if chemistry_data:
        print(f"\n🎉 提取完成！")
        print(f"📊 统计信息:")
        print(f"   - 总条数: {len(chemistry_data)}")
        
        # 统计答案分布
        answer_counts = {}
        for item in chemistry_data:
            answer = item['answer']
            answer_counts[answer] = answer_counts.get(answer, 0) + 1
        
        print(f"   - 答案分布: {answer_counts}")
        
        # 统计来源分布
        src_counts = {}
        for item in chemistry_data:
            src = item['src']
            src_counts[src] = src_counts.get(src, 0) + 1
        
        print(f"   - 来源分布: {src_counts}")

if __name__ == "__main__":
    main()
