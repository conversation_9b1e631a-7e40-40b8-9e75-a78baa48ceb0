## 3. REVEALING CHEMICAL REASONING IN LLMS THROUGH SEARCH ON COMPLEX PLANNING TASKS

论文题目：REVEALING CHEMICAL REASONING IN LLMS THROUGH SEARCH ON COMPLEX PLANNING TASKS

## 3.1 主要任务

提出LLM直接生成smiles表现不佳

1. **提示词导向的逆合成规划：**

输入：目标分子、prompt（描述期望合成策略）【包括关于所需转化 类型的详细信息、可行性评估、所需键断开和反应可能发生的合成阶段，或更广泛的合成 策略、条件等】

* **路线重排：**

  * 输入：目标分子、自然语言查询、一组合成路线

  * 目标：LLM打分，分数标识路线与查询的契合度

  * 角色：LLM不对路线生成过程进行干预，只从已有结果筛选最佳

* **基于LLM的搜索：**

  * 输入：目标分子、自然语言查询

  * 目标：LLM结合MCTS等搜索算法，生成符合查询要求的路线，LLM充当价值函数

  * 角色：LLM作为引导者，引导搜索方向

2. **机理解释生成：**

   * 输入：反应物、产物
   * 目标：LLM引导搜索算法，找出一系列合理的、由“离子化”和“进攻”等基本步骤组成的反应机理，最终连接反应物和产物。

## 3.2 机理解释

**反应机理：**通过一系列基本步骤来指定给定化学变化的原因和方式

1. **问题简化：**将复杂反应机理分为基本化学步骤：
   * **电离（Ionization Moves）**：即**化学键的断裂**。一个化学键的成键电子对离开，转移到其中一个原子上，形成一个正离子和一个负离子（或一个带孤对电子的中性分子）。
   * **进攻（Attack Moves）**：即**化学键的形成**。一个拥有孤对电子的原子（亲核体）去“进攻”一个缺少电子的原子（亲电体），形成一个新的化学键。
2. **路径搜索：**将寻找反应机理任务转为搜索任务，起点是反应物，终点是生成物，每一步都是上述基本化学步骤，目标是寻找所有可能路径中化学上最合理、最可能发生的那一条
3. **单步评分：**LLM对所有可能的下一步进行“化学合理性评分”