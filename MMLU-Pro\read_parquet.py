#!/usr/bin/env python3
"""
脚本用于读取和分析 MMLU-Pro 数据集的 Parquet 文件
"""

import pandas as pd
import os
import sys

def read_parquet_file(file_path):
    """
    读取 Parquet 文件并显示基本信息
    
    Args:
        file_path (str): Parquet 文件路径
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误: 文件 {file_path} 不存在")
            return None
        
        print(f"正在读取文件: {file_path}")
        print("=" * 50)
        
        # 读取 Parquet 文件
        df = pd.read_parquet(file_path)
        
        # 显示基本信息
        print(f"数据形状: {df.shape}")
        print(f"列数: {len(df.columns)}")
        print(f"行数: {len(df)}")
        print("\n列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i}. {col}")
        
        print("\n数据类型:")
        print(df.dtypes)
        
        print("\n前5行数据:")
        print(df.head())
        
        # 如果数据量不大，显示更多信息
        if len(df) <= 1000:
            print(f"\n数据概览:")
            print(df.info())
            
            print(f"\n数值列统计:")
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                print(df[numeric_cols].describe())
            else:
                print("没有数值列")
        
        # 检查是否有缺失值
        missing_values = df.isnull().sum()
        if missing_values.sum() > 0:
            print(f"\n缺失值统计:")
            print(missing_values[missing_values > 0])
        else:
            print(f"\n没有缺失值")
        
        return df
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def explore_data(df):
    """
    进一步探索数据
    
    Args:
        df (pd.DataFrame): 数据框
    """
    if df is None:
        return
    
    print("\n" + "=" * 50)
    print("数据探索")
    print("=" * 50)
    
    # 显示每列的唯一值数量
    print("\n每列唯一值数量:")
    for col in df.columns:
        unique_count = df[col].nunique()
        print(f"  {col}: {unique_count}")
    
    # 如果有文本列，显示一些示例
    text_cols = df.select_dtypes(include=['object']).columns
    if len(text_cols) > 0:
        print(f"\n文本列示例:")
        for col in text_cols[:3]:  # 只显示前3个文本列
            print(f"\n{col} 列的前3个值:")
            for i, value in enumerate(df[col].head(3)):
                print(f"  {i+1}. {str(value)[:200]}...")  # 限制显示长度

def save_sample_data(df, output_file="sample_data.csv"):
    """
    保存样本数据到CSV文件
    
    Args:
        df (pd.DataFrame): 数据框
        output_file (str): 输出文件名
    """
    if df is None:
        return
    
    try:
        # 保存前100行到CSV
        sample_size = min(100, len(df))
        df.head(sample_size).to_csv(output_file, index=False, encoding='utf-8')
        print(f"\n已保存前 {sample_size} 行数据到 {output_file}")
    except Exception as e:
        print(f"保存样本数据时出错: {e}")

def main():
    """主函数"""
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Parquet 文件路径（与脚本同级）
    parquet_file = os.path.join(script_dir, "test-00000-of-00001.parquet")
    
    print("MMLU-Pro Parquet 文件读取器")
    print("=" * 50)
    
    # 读取文件
    df = read_parquet_file(parquet_file)
    
    if df is not None:
        # 探索数据
        explore_data(df)
        
        # 保存样本数据
        sample_file = os.path.join(script_dir, "sample_data.csv")
        save_sample_data(df, sample_file)
        
        # 交互式查询
        print("\n" + "=" * 50)
        print("交互式查询 (输入 'quit' 退出)")
        print("=" * 50)
        
        while True:
            try:
                query = input("\n请输入要查看的行号范围 (例如: 0-5) 或 'quit' 退出: ").strip()
                
                if query.lower() == 'quit':
                    break
                
                if '-' in query:
                    start, end = map(int, query.split('-'))
                    print(f"\n第 {start} 到 {end} 行数据:")
                    print(df.iloc[start:end+1])
                else:
                    row_num = int(query)
                    print(f"\n第 {row_num} 行数据:")
                    print(df.iloc[row_num])
                    
            except (ValueError, IndexError) as e:
                print(f"输入错误: {e}")
            except KeyboardInterrupt:
                break
    
    print("\n程序结束")

if __name__ == "__main__":
    # 检查是否安装了必要的库
    try:
        import pandas as pd
    except ImportError:
        print("错误: 需要安装 pandas 库")
        print("请运行: pip install pandas pyarrow")
        sys.exit(1)
    
    try:
        import pyarrow
    except ImportError:
        print("警告: 建议安装 pyarrow 库以获得更好的性能")
        print("请运行: pip install pyarrow")
    
    main()
